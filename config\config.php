<?php
/**
 * Application Configuration
 */

// Start session with secure settings
if (session_status() == PHP_SESSION_NONE) {
    ini_set('session.cookie_httponly', 1);
    ini_set('session.cookie_secure', 1);
    ini_set('session.use_only_cookies', 1);
    session_start();
}

// Application settings
define('APP_NAME', 'Portal Application');
define('APP_VERSION', '1.0.0');
define('BASE_URL', 'http://localhost/portal-dev/');

// Security settings
define('CSRF_TOKEN_NAME', 'csrf_token');
define('SESSION_TIMEOUT', 3600); // 1 hour

// FTP Configuration
define('FTP_HOST', 'localhost');
define('FTP_USERNAME', 'ftp_user');
define('FTP_PASSWORD', 'ftp_password');
define('FTP_BASE_PATH', '/invoices/');

// Payment Gateway Configuration (PayFast)
define('PAYFAST_MERCHANT_ID', 'your_merchant_id');
define('PAYFAST_MERCHANT_KEY', 'your_merchant_key');
define('PAYFAST_PASSPHRASE', 'your_passphrase');
define('PAYFAST_SANDBOX', true); // Set to false for production

// Available modules
define('AVAILABLE_MODULES', [
    'invoices' => 'Invoices',
    'freight' => 'Freight System',
    'tariff' => 'Tariff Book',
    'accounting' => 'Accounting',
    'backoffice' => 'Back Office'
]);

// Error reporting (disable in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include database configuration
require_once __DIR__ . '/database.php';

/**
 * Generate CSRF token
 */
function generateCSRFToken() {
    if (!isset($_SESSION[CSRF_TOKEN_NAME])) {
        $_SESSION[CSRF_TOKEN_NAME] = bin2hex(random_bytes(32));
    }
    return $_SESSION[CSRF_TOKEN_NAME];
}

/**
 * Verify CSRF token
 */
function verifyCSRFToken($token) {
    return isset($_SESSION[CSRF_TOKEN_NAME]) && hash_equals($_SESSION[CSRF_TOKEN_NAME], $token);
}

/**
 * Sanitize input data
 */
function sanitizeInput($data) {
    return htmlspecialchars(strip_tags(trim($data)), ENT_QUOTES, 'UTF-8');
}

/**
 * Check if user is logged in
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']) && isset($_SESSION['last_activity']) && 
           (time() - $_SESSION['last_activity'] < SESSION_TIMEOUT);
}

/**
 * Check if user is admin
 */
function isAdmin() {
    return isLoggedIn() && $_SESSION['role'] === 'admin';
}

/**
 * Redirect to login if not authenticated
 */
function requireLogin() {
    if (!isLoggedIn()) {
        header('Location: ' . BASE_URL . 'login.php');
        exit();
    }
    $_SESSION['last_activity'] = time();
}

/**
 * Redirect to login if not admin
 */
function requireAdmin() {
    requireLogin();
    if (!isAdmin()) {
        header('Location: ' . BASE_URL . 'dashboard.php');
        exit();
    }
}
?>
