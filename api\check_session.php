<?php
/**
 * Check Session API
 * Validates current session status
 */

require_once '../config/config.php';

header('Content-Type: application/json');

$response = [
    'valid' => false,
    'message' => 'Session invalid'
];

if (isLoggedIn()) {
    $response = [
        'valid' => true,
        'message' => 'Session valid',
        'user_id' => $_SESSION['user_id'],
        'expires_in' => SESSION_TIMEOUT - (time() - $_SESSION['last_activity'])
    ];
    
    // Update last activity
    $_SESSION['last_activity'] = time();
}

echo json_encode($response);
?>
