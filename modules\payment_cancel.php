<?php
/**
 * Payment Cancel Handler
 * Handles user cancellation from PayFast
 */

require_once '../config/config.php';
require_once '../classes/PaymentGateway.php';

// Require login
requireLogin();

$database = new Database();
$db = $database->getConnection();
$payment_gateway = new PaymentGateway($db);

$payment_reference = '';
$invoice_name = '';

// Check if we have payment reference
if (isset($_GET['custom_str1'])) {
    $payment_reference = sanitizeInput($_GET['custom_str1']);
} elseif (isset($_POST['custom_str1'])) {
    $payment_reference = sanitizeInput($_POST['custom_str1']);
}

if ($payment_reference) {
    // Update payment status to cancelled
    $payment_gateway->updatePaymentStatus($payment_reference, 'cancelled');
    
    // Get payment details
    $payment = $payment_gateway->getPaymentByReference($payment_reference);
    if ($payment) {
        $invoice_name = $payment['invoice_name'];
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Cancelled - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body class="dashboard">
    <header class="header">
        <div class="header-content">
            <h1><?php echo APP_NAME; ?> - Payment Cancelled</h1>
            <div class="user-info">
                <a href="invoices.php" class="btn btn-sm btn-secondary">Back to Invoices</a>
                <a href="../dashboard.php" class="btn btn-sm btn-secondary">Dashboard</a>
                <a href="../logout.php" class="btn btn-sm btn-danger">Logout</a>
            </div>
        </div>
    </header>
    
    <main class="main-content">
        <div class="cancel-container">
            <div class="cancel-card">
                <div class="cancel-icon">⚠️</div>
                <h2>Payment Cancelled</h2>
                <p>You have cancelled the payment process. No charges have been made to your account.</p>
                
                <?php if ($invoice_name): ?>
                    <div class="invoice-info">
                        <p><strong>Invoice:</strong> <?php echo htmlspecialchars($invoice_name); ?></p>
                        <p>This invoice remains unpaid and is available for payment at any time.</p>
                    </div>
                <?php endif; ?>
                
                <div class="next-steps">
                    <h3>What you can do next:</h3>
                    <ul>
                        <li>Return to the invoices page to view all your invoices</li>
                        <li>Try the payment again when you're ready</li>
                        <li>Contact support if you need assistance</li>
                        <li>Use a different payment method if needed</li>
                    </ul>
                </div>
                
                <div class="action-buttons">
                    <a href="invoices.php" class="btn btn-primary">View Invoices</a>
                    <?php if ($invoice_name): ?>
                        <a href="payment.php?invoice=<?php echo urlencode($invoice_name); ?>" class="btn btn-secondary">Try Payment Again</a>
                    <?php endif; ?>
                    <a href="../dashboard.php" class="btn btn-secondary">Dashboard</a>
                </div>
            </div>
        </div>
    </main>
    
    <script src="../assets/js/script.js"></script>
</body>
</html>

<style>
.cancel-container {
    max-width: 600px;
    margin: 0 auto;
    padding-top: 2rem;
}

.cancel-card {
    background: white;
    padding: 3rem 2rem;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    text-align: center;
    border-top: 5px solid #ffc107;
}

.cancel-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.cancel-card h2 {
    margin-bottom: 1rem;
    color: #333;
}

.cancel-card p {
    color: #666;
    margin-bottom: 1.5rem;
}

.invoice-info {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 2rem;
}

.invoice-info p {
    margin-bottom: 0.5rem;
}

.invoice-info p:last-child {
    margin-bottom: 0;
}

.next-steps {
    text-align: left;
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 2rem;
}

.next-steps h3 {
    margin-bottom: 1rem;
    color: #333;
}

.next-steps ul {
    margin: 0;
    padding-left: 1.5rem;
}

.next-steps li {
    margin-bottom: 0.5rem;
    color: #666;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

@media (max-width: 768px) {
    .cancel-card {
        padding: 2rem 1rem;
    }
    
    .action-buttons {
        flex-direction: column;
    }
}
</style>
