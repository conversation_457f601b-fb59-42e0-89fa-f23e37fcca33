/**
 * Admin Panel JavaScript
 * Functionality for user management
 */

/**
 * Edit user
 */
function editUser(userId) {
    // Fetch user data
    fetch(`../api/get_user.php?id=${userId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showEditUserModal(data.user);
            } else {
                showAlert('Failed to load user data.', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('Failed to load user data.', 'error');
        });
}

/**
 * Show edit user modal
 */
function showEditUserModal(user) {
    const modal = createModal('Edit User', `
        <form id="editUserForm" method="POST">
            <input type="hidden" name="csrf_token" value="${document.querySelector('input[name="csrf_token"]').value}">
            <input type="hidden" name="action" value="edit_user">
            <input type="hidden" name="user_id" value="${user.id}">
            
            <div class="form-row">
                <div class="form-group">
                    <label for="edit_name">Full Name</label>
                    <input type="text" id="edit_name" name="name" value="${user.name}" required>
                </div>
                
                <div class="form-group">
                    <label for="edit_email">Email Address</label>
                    <input type="email" id="edit_email" name="email" value="${user.email}" required>
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="edit_role">Role</label>
                    <select id="edit_role" name="role" required>
                        <option value="user" ${user.role === 'user' ? 'selected' : ''}>User</option>
                        <option value="admin" ${user.role === 'admin' ? 'selected' : ''}>Admin</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="edit_status">Status</label>
                    <select id="edit_status" name="status" required>
                        <option value="active" ${user.status === 'active' ? 'selected' : ''}>Active</option>
                        <option value="inactive" ${user.status === 'inactive' ? 'selected' : ''}>Inactive</option>
                    </select>
                </div>
            </div>
            
            <div class="form-group">
                <label for="edit_client_id">Client</label>
                <select id="edit_client_id" name="client_id" required>
                    <!-- Will be populated by JavaScript -->
                </select>
            </div>
            
            <div class="form-group">
                <label>Module Access Rights</label>
                <div class="checkbox-group" id="edit_modules">
                    <!-- Will be populated by JavaScript -->
                </div>
            </div>
            
            <div class="form-actions">
                <button type="button" onclick="closeModal()" class="btn btn-secondary">Cancel</button>
                <button type="submit" class="btn btn-primary">Update User</button>
            </div>
        </form>
    `);
    
    // Populate clients dropdown
    populateClientsDropdown('edit_client_id', user.client_id);
    
    // Populate modules checkboxes
    populateModulesCheckboxes('edit_modules', user.rights);
    
    // Handle form submission
    document.getElementById('editUserForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        
        fetch(window.location.href, {
            method: 'POST',
            body: formData
        })
        .then(response => response.text())
        .then(data => {
            // Reload page to show updated data
            window.location.reload();
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('Failed to update user.', 'error');
        });
    });
}

/**
 * Delete user
 */
function deleteUser(userId) {
    if (confirmAction('Are you sure you want to delete this user? This action cannot be undone.')) {
        const formData = new FormData();
        formData.append('csrf_token', document.querySelector('input[name="csrf_token"]').value);
        formData.append('action', 'delete_user');
        formData.append('user_id', userId);
        
        fetch(window.location.href, {
            method: 'POST',
            body: formData
        })
        .then(response => response.text())
        .then(data => {
            // Reload page to show updated data
            window.location.reload();
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('Failed to delete user.', 'error');
        });
    }
}

/**
 * Create modal
 */
function createModal(title, content) {
    // Remove existing modal
    const existingModal = document.getElementById('modal');
    if (existingModal) {
        existingModal.remove();
    }
    
    const modal = document.createElement('div');
    modal.id = 'modal';
    modal.className = 'modal';
    modal.style.display = 'block';
    
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>${title}</h3>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            <div class="modal-body">
                ${content}
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Close modal when clicking outside
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeModal();
        }
    });
    
    return modal;
}

/**
 * Close modal
 */
function closeModal() {
    const modal = document.getElementById('modal');
    if (modal) {
        modal.remove();
    }
}

/**
 * Populate clients dropdown
 */
function populateClientsDropdown(selectId, selectedClientId) {
    fetch('../api/get_clients.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const select = document.getElementById(selectId);
                select.innerHTML = '<option value="">Select Client</option>';
                
                data.clients.forEach(client => {
                    const option = document.createElement('option');
                    option.value = client.id;
                    option.textContent = client.name;
                    if (client.id == selectedClientId) {
                        option.selected = true;
                    }
                    select.appendChild(option);
                });
            }
        })
        .catch(error => {
            console.error('Error loading clients:', error);
        });
}

/**
 * Populate modules checkboxes
 */
function populateModulesCheckboxes(containerId, userRights) {
    const modules = {
        'invoices': 'Invoices',
        'freight': 'Freight System',
        'tariff': 'Tariff Book',
        'accounting': 'Accounting',
        'backoffice': 'Back Office'
    };
    
    const container = document.getElementById(containerId);
    container.innerHTML = '';
    
    Object.keys(modules).forEach(moduleKey => {
        const label = document.createElement('label');
        label.className = 'checkbox-label';
        
        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.name = 'modules[]';
        checkbox.value = moduleKey;
        
        if (userRights && userRights.includes(moduleKey)) {
            checkbox.checked = true;
        }
        
        label.appendChild(checkbox);
        label.appendChild(document.createTextNode(modules[moduleKey]));
        
        container.appendChild(label);
    });
}

// Initialize admin panel
document.addEventListener('DOMContentLoaded', function() {
    // Add keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Escape key closes modal
        if (e.key === 'Escape') {
            closeModal();
        }
    });
});
