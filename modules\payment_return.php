<?php
/**
 * Payment Return Handler
 * Handles user return from PayFast after payment
 */

require_once '../config/config.php';
require_once '../classes/PaymentGateway.php';

// Require login
requireLogin();

$database = new Database();
$db = $database->getConnection();
$payment_gateway = new PaymentGateway($db);

$payment_status = 'unknown';
$payment_reference = '';
$invoice_name = '';
$amount = 0;

// Check if we have payment reference in the URL or POST data
if (isset($_GET['custom_str1'])) {
    $payment_reference = sanitizeInput($_GET['custom_str1']);
} elseif (isset($_POST['custom_str1'])) {
    $payment_reference = sanitizeInput($_POST['custom_str1']);
}

if ($payment_reference) {
    // Get payment details
    $payment = $payment_gateway->getPaymentByReference($payment_reference);
    
    if ($payment) {
        $payment_status = $payment['payment_status'];
        $invoice_name = $payment['invoice_name'];
        $amount = $payment['amount'];
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Result - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body class="dashboard">
    <header class="header">
        <div class="header-content">
            <h1><?php echo APP_NAME; ?> - Payment Result</h1>
            <div class="user-info">
                <a href="invoices.php" class="btn btn-sm btn-secondary">Back to Invoices</a>
                <a href="../dashboard.php" class="btn btn-sm btn-secondary">Dashboard</a>
                <a href="../logout.php" class="btn btn-sm btn-danger">Logout</a>
            </div>
        </div>
    </header>
    
    <main class="main-content">
        <div class="payment-result-container">
            <?php if ($payment_status === 'completed'): ?>
                <div class="result-card success">
                    <div class="result-icon">✅</div>
                    <h2>Payment Successful!</h2>
                    <p>Your payment has been processed successfully.</p>
                    
                    <div class="payment-details">
                        <div class="detail-row">
                            <span class="label">Invoice:</span>
                            <span class="value"><?php echo htmlspecialchars($invoice_name); ?></span>
                        </div>
                        <div class="detail-row">
                            <span class="label">Amount Paid:</span>
                            <span class="value">R<?php echo number_format($amount, 2); ?></span>
                        </div>
                        <div class="detail-row">
                            <span class="label">Payment Reference:</span>
                            <span class="value"><?php echo htmlspecialchars($payment_reference); ?></span>
                        </div>
                        <div class="detail-row">
                            <span class="label">Status:</span>
                            <span class="value status-completed">Completed</span>
                        </div>
                    </div>
                    
                    <div class="next-steps">
                        <h3>What happens next?</h3>
                        <ul>
                            <li>A payment confirmation file has been created in your client folder</li>
                            <li>You will receive an email confirmation shortly</li>
                            <li>The invoice status has been updated to "Paid"</li>
                            <li>You can download your invoice receipt from the invoices page</li>
                        </ul>
                    </div>
                </div>
                
            <?php elseif ($payment_status === 'failed'): ?>
                <div class="result-card error">
                    <div class="result-icon">❌</div>
                    <h2>Payment Failed</h2>
                    <p>Unfortunately, your payment could not be processed.</p>
                    
                    <div class="payment-details">
                        <div class="detail-row">
                            <span class="label">Invoice:</span>
                            <span class="value"><?php echo htmlspecialchars($invoice_name); ?></span>
                        </div>
                        <div class="detail-row">
                            <span class="label">Amount:</span>
                            <span class="value">R<?php echo number_format($amount, 2); ?></span>
                        </div>
                        <div class="detail-row">
                            <span class="label">Payment Reference:</span>
                            <span class="value"><?php echo htmlspecialchars($payment_reference); ?></span>
                        </div>
                        <div class="detail-row">
                            <span class="label">Status:</span>
                            <span class="value status-failed">Failed</span>
                        </div>
                    </div>
                    
                    <div class="next-steps">
                        <h3>What you can do:</h3>
                        <ul>
                            <li>Check your card details and try again</li>
                            <li>Ensure you have sufficient funds</li>
                            <li>Contact your bank if the problem persists</li>
                            <li>Try using a different payment method</li>
                        </ul>
                    </div>
                </div>
                
            <?php elseif ($payment_status === 'cancelled'): ?>
                <div class="result-card warning">
                    <div class="result-icon">⚠️</div>
                    <h2>Payment Cancelled</h2>
                    <p>You cancelled the payment process.</p>
                    
                    <div class="payment-details">
                        <div class="detail-row">
                            <span class="label">Invoice:</span>
                            <span class="value"><?php echo htmlspecialchars($invoice_name); ?></span>
                        </div>
                        <div class="detail-row">
                            <span class="label">Amount:</span>
                            <span class="value">R<?php echo number_format($amount, 2); ?></span>
                        </div>
                        <div class="detail-row">
                            <span class="label">Status:</span>
                            <span class="value status-cancelled">Cancelled</span>
                        </div>
                    </div>
                    
                    <div class="next-steps">
                        <h3>Next steps:</h3>
                        <ul>
                            <li>You can try the payment again at any time</li>
                            <li>The invoice remains unpaid and available for payment</li>
                            <li>No charges have been made to your account</li>
                        </ul>
                    </div>
                </div>
                
            <?php elseif ($payment_status === 'pending'): ?>
                <div class="result-card info">
                    <div class="result-icon">⏳</div>
                    <h2>Payment Pending</h2>
                    <p>Your payment is being processed. Please wait for confirmation.</p>
                    
                    <div class="payment-details">
                        <div class="detail-row">
                            <span class="label">Invoice:</span>
                            <span class="value"><?php echo htmlspecialchars($invoice_name); ?></span>
                        </div>
                        <div class="detail-row">
                            <span class="label">Amount:</span>
                            <span class="value">R<?php echo number_format($amount, 2); ?></span>
                        </div>
                        <div class="detail-row">
                            <span class="label">Payment Reference:</span>
                            <span class="value"><?php echo htmlspecialchars($payment_reference); ?></span>
                        </div>
                        <div class="detail-row">
                            <span class="label">Status:</span>
                            <span class="value status-pending">Pending</span>
                        </div>
                    </div>
                    
                    <div class="next-steps">
                        <h3>Please note:</h3>
                        <ul>
                            <li>Payment processing can take a few minutes</li>
                            <li>You will receive confirmation once processed</li>
                            <li>Check your email for updates</li>
                            <li>Refresh the invoices page to see updated status</li>
                        </ul>
                    </div>
                </div>
                
            <?php else: ?>
                <div class="result-card error">
                    <div class="result-icon">❓</div>
                    <h2>Payment Status Unknown</h2>
                    <p>We could not determine the status of your payment.</p>
                    
                    <div class="next-steps">
                        <h3>What to do:</h3>
                        <ul>
                            <li>Check your email for payment confirmation</li>
                            <li>Review the invoices page for updated status</li>
                            <li>Contact support if you need assistance</li>
                            <li>Keep your payment reference for future reference</li>
                        </ul>
                    </div>
                </div>
            <?php endif; ?>
            
            <div class="action-buttons">
                <a href="invoices.php" class="btn btn-primary">View Invoices</a>
                <?php if ($payment_status !== 'completed' && $invoice_name): ?>
                    <a href="payment.php?invoice=<?php echo urlencode($invoice_name); ?>" class="btn btn-secondary">Try Again</a>
                <?php endif; ?>
                <a href="../dashboard.php" class="btn btn-secondary">Dashboard</a>
            </div>
        </div>
    </main>
    
    <script src="../assets/js/script.js"></script>
    <script>
        // Auto-refresh page every 30 seconds if payment is pending
        <?php if ($payment_status === 'pending'): ?>
        setTimeout(function() {
            window.location.reload();
        }, 30000);
        <?php endif; ?>
    </script>
</body>
</html>

<style>
.payment-result-container {
    max-width: 600px;
    margin: 0 auto;
}

.result-card {
    background: white;
    padding: 3rem 2rem;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    text-align: center;
    margin-bottom: 2rem;
}

.result-card.success {
    border-top: 5px solid #28a745;
}

.result-card.error {
    border-top: 5px solid #dc3545;
}

.result-card.warning {
    border-top: 5px solid #ffc107;
}

.result-card.info {
    border-top: 5px solid #17a2b8;
}

.result-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.result-card h2 {
    margin-bottom: 1rem;
    color: #333;
}

.result-card p {
    color: #666;
    margin-bottom: 2rem;
}

.payment-details {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 2rem;
    text-align: left;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.75rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid #dee2e6;
}

.detail-row:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.detail-row .label {
    font-weight: 600;
    color: #666;
}

.detail-row .value {
    color: #333;
}

.status-completed {
    color: #28a745;
    font-weight: 600;
}

.status-failed {
    color: #dc3545;
    font-weight: 600;
}

.status-cancelled {
    color: #ffc107;
    font-weight: 600;
}

.status-pending {
    color: #17a2b8;
    font-weight: 600;
}

.next-steps {
    text-align: left;
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
}

.next-steps h3 {
    margin-bottom: 1rem;
    color: #333;
}

.next-steps ul {
    margin: 0;
    padding-left: 1.5rem;
}

.next-steps li {
    margin-bottom: 0.5rem;
    color: #666;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

@media (max-width: 768px) {
    .result-card {
        padding: 2rem 1rem;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .detail-row {
        flex-direction: column;
        gap: 0.25rem;
    }
}
</style>
