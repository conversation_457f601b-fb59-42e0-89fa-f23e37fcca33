<?php
/**
 * Payment Notification Handler
 * Handles PayFast ITN (Instant Transaction Notification)
 */

require_once '../config/config.php';
require_once '../classes/PaymentGateway.php';

// Disable output buffering
if (ob_get_level()) {
    ob_end_clean();
}

// Log the notification
error_log("PayFast ITN received: " . json_encode($_POST));

// Validate that we have POST data
if (empty($_POST)) {
    error_log("PayFast ITN: No POST data received");
    http_response_code(400);
    exit();
}

$database = new Database();
$db = $database->getConnection();
$payment_gateway = new PaymentGateway($db);

try {
    // Verify the payment notification
    if (!$payment_gateway->verifyPaymentNotification($_POST)) {
        error_log("PayFast ITN: Verification failed");
        http_response_code(400);
        exit();
    }
    
    // Extract payment data
    $payment_reference = $_POST['custom_str1'];
    $payment_status = $_POST['payment_status'];
    $pf_payment_id = $_POST['pf_payment_id'];
    $amount_gross = $_POST['amount_gross'];
    
    // Log the verified notification
    error_log("PayFast ITN verified: Reference={$payment_reference}, Status={$payment_status}, Amount={$amount_gross}");
    
    // Process based on payment status
    switch ($payment_status) {
        case 'COMPLETE':
            // Process successful payment
            if ($payment_gateway->processSuccessfulPayment($payment_reference, $pf_payment_id)) {
                error_log("PayFast ITN: Payment processed successfully - {$payment_reference}");
            } else {
                error_log("PayFast ITN: Failed to process payment - {$payment_reference}");
            }
            break;
            
        case 'FAILED':
            $payment_gateway->updatePaymentStatus($payment_reference, 'failed', $pf_payment_id);
            error_log("PayFast ITN: Payment failed - {$payment_reference}");
            break;
            
        case 'CANCELLED':
            $payment_gateway->updatePaymentStatus($payment_reference, 'cancelled', $pf_payment_id);
            error_log("PayFast ITN: Payment cancelled - {$payment_reference}");
            break;
            
        default:
            error_log("PayFast ITN: Unknown payment status - {$payment_status}");
            break;
    }
    
    // Respond with HTTP 200 OK
    http_response_code(200);
    echo "OK";
    
} catch (Exception $e) {
    error_log("PayFast ITN Error: " . $e->getMessage());
    http_response_code(500);
    echo "ERROR";
}
?>
