<?php
/**
 * User Class
 * Handles user authentication and management
 */

require_once __DIR__ . '/../config/config.php';

class User {
    private $conn;
    private $table_name = "users";
    
    public $id;
    public $name;
    public $email;
    public $password;
    public $role;
    public $client_id;
    public $status;
    
    public function __construct($db) {
        $this->conn = $db;
    }
    
    /**
     * Authenticate user login
     */
    public function login($email, $password) {
        $query = "SELECT u.id, u.name, u.email, u.password, u.role, u.client_id, u.status, c.name as client_name 
                  FROM " . $this->table_name . " u 
                  LEFT JOIN clients c ON u.client_id = c.id 
                  WHERE u.email = :email AND u.status = 'active'";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':email', $email);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (password_verify($password, $row['password'])) {
                // Update last login
                $this->updateLastLogin($row['id']);
                
                // Set session variables
                $_SESSION['user_id'] = $row['id'];
                $_SESSION['user_name'] = $row['name'];
                $_SESSION['user_email'] = $row['email'];
                $_SESSION['role'] = $row['role'];
                $_SESSION['client_id'] = $row['client_id'];
                $_SESSION['client_name'] = $row['client_name'];
                $_SESSION['last_activity'] = time();
                
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Create new user
     */
    public function create() {
        $query = "INSERT INTO " . $this->table_name . " 
                  SET name = :name, email = :email, password = :password, 
                      role = :role, client_id = :client_id, status = :status";
        
        $stmt = $this->conn->prepare($query);
        
        // Hash password
        $hashed_password = password_hash($this->password, PASSWORD_DEFAULT);
        
        $stmt->bindParam(':name', $this->name);
        $stmt->bindParam(':email', $this->email);
        $stmt->bindParam(':password', $hashed_password);
        $stmt->bindParam(':role', $this->role);
        $stmt->bindParam(':client_id', $this->client_id);
        $stmt->bindParam(':status', $this->status);
        
        if ($stmt->execute()) {
            $this->id = $this->conn->lastInsertId();
            return true;
        }
        
        return false;
    }
    
    /**
     * Get user by ID
     */
    public function getById($id) {
        $query = "SELECT u.*, c.name as client_name 
                  FROM " . $this->table_name . " u 
                  LEFT JOIN clients c ON u.client_id = c.id 
                  WHERE u.id = :id";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            return $stmt->fetch(PDO::FETCH_ASSOC);
        }
        
        return false;
    }
    
    /**
     * Get all users (admin only)
     */
    public function getAll() {
        $query = "SELECT u.*, c.name as client_name 
                  FROM " . $this->table_name . " u 
                  LEFT JOIN clients c ON u.client_id = c.id 
                  ORDER BY u.created_at DESC";
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Update user
     */
    public function update() {
        $query = "UPDATE " . $this->table_name . " 
                  SET name = :name, email = :email, role = :role, 
                      client_id = :client_id, status = :status 
                  WHERE id = :id";
        
        $stmt = $this->conn->prepare($query);
        
        $stmt->bindParam(':name', $this->name);
        $stmt->bindParam(':email', $this->email);
        $stmt->bindParam(':role', $this->role);
        $stmt->bindParam(':client_id', $this->client_id);
        $stmt->bindParam(':status', $this->status);
        $stmt->bindParam(':id', $this->id);
        
        return $stmt->execute();
    }
    
    /**
     * Delete user
     */
    public function delete($id) {
        $query = "DELETE FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        
        return $stmt->execute();
    }
    
    /**
     * Update last login timestamp
     */
    private function updateLastLogin($user_id) {
        $query = "UPDATE " . $this->table_name . " SET last_login = NOW() WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $user_id);
        $stmt->execute();
    }
    
    /**
     * Check if email exists
     */
    public function emailExists($email, $exclude_id = null) {
        $query = "SELECT id FROM " . $this->table_name . " WHERE email = :email";
        
        if ($exclude_id) {
            $query .= " AND id != :exclude_id";
        }
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':email', $email);
        
        if ($exclude_id) {
            $stmt->bindParam(':exclude_id', $exclude_id);
        }
        
        $stmt->execute();
        
        return $stmt->rowCount() > 0;
    }
    
    /**
     * Get user rights
     */
    public function getUserRights($user_id) {
        $query = "SELECT module_name FROM user_rights WHERE user_id = :user_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
        
        $rights = [];
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $rights[] = $row['module_name'];
        }
        
        return $rights;
    }
    
    /**
     * Set user rights
     */
    public function setUserRights($user_id, $modules) {
        // First, delete existing rights
        $delete_query = "DELETE FROM user_rights WHERE user_id = :user_id";
        $delete_stmt = $this->conn->prepare($delete_query);
        $delete_stmt->bindParam(':user_id', $user_id);
        $delete_stmt->execute();
        
        // Then, insert new rights
        if (!empty($modules)) {
            $insert_query = "INSERT INTO user_rights (user_id, module_name) VALUES (:user_id, :module_name)";
            $insert_stmt = $this->conn->prepare($insert_query);
            
            foreach ($modules as $module) {
                $insert_stmt->bindParam(':user_id', $user_id);
                $insert_stmt->bindParam(':module_name', $module);
                $insert_stmt->execute();
            }
        }
        
        return true;
    }
    
    /**
     * Logout user
     */
    public static function logout() {
        session_unset();
        session_destroy();
        header('Location: ' . BASE_URL . 'login.php');
        exit();
    }
}
?>
