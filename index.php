<?php
/**
 * Main Entry Point
 * Redirects to appropriate page based on installation status
 */

// Check if application is installed
if (!file_exists('config/installed.lock')) {
    // Not installed, redirect to installation
    header('Location: install.php');
    exit();
}

// Check if user is already logged in
require_once 'config/config.php';

if (isLoggedIn()) {
    // User is logged in, redirect to dashboard
    header('Location: dashboard.php');
    exit();
} else {
    // User not logged in, redirect to login page
    header('Location: login.php');
    exit();
}
?>
