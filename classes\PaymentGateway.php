<?php
/**
 * Payment Gateway Class
 * Handles PayFast payment integration
 */

require_once __DIR__ . '/../config/config.php';

class PaymentGateway {
    private $merchant_id;
    private $merchant_key;
    private $passphrase;
    private $sandbox;
    private $db;
    
    public function __construct($db) {
        $this->merchant_id = PAYFAST_MERCHANT_ID;
        $this->merchant_key = PAYFAST_MERCHANT_KEY;
        $this->passphrase = PAYFAST_PASSPHRASE;
        $this->sandbox = PAYFAST_SANDBOX;
        $this->db = $db;
    }
    
    /**
     * Create payment record
     */
    public function createPayment($invoice_name, $user_id, $client_id, $amount) {
        $query = "INSERT INTO payments (invoice_name, user_id, client_id, amount, payment_status, payment_reference, payment_gateway) 
                  VALUES (:invoice_name, :user_id, :client_id, :amount, 'pending', :payment_reference, 'payfast')";
        
        $payment_reference = $this->generatePaymentReference();
        
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':invoice_name', $invoice_name);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':client_id', $client_id);
        $stmt->bindParam(':amount', $amount);
        $stmt->bindParam(':payment_reference', $payment_reference);
        
        if ($stmt->execute()) {
            return [
                'payment_id' => $this->db->lastInsertId(),
                'payment_reference' => $payment_reference
            ];
        }
        
        return false;
    }
    
    /**
     * Generate PayFast payment form
     */
    public function generatePaymentForm($payment_data) {
        $data = [
            'merchant_id' => $this->merchant_id,
            'merchant_key' => $this->merchant_key,
            'return_url' => BASE_URL . 'modules/payment_return.php',
            'cancel_url' => BASE_URL . 'modules/payment_cancel.php',
            'notify_url' => BASE_URL . 'modules/payment_notify.php',
            'name_first' => $payment_data['user_name'],
            'email_address' => $payment_data['user_email'],
            'item_name' => 'Invoice Payment: ' . $payment_data['invoice_name'],
            'item_description' => 'Payment for invoice ' . $payment_data['invoice_name'],
            'amount' => number_format($payment_data['amount'], 2, '.', ''),
            'custom_str1' => $payment_data['payment_reference'],
            'custom_str2' => $payment_data['client_id'],
            'custom_str3' => $payment_data['user_id']
        ];
        
        // Generate signature
        $data['signature'] = $this->generateSignature($data);
        
        // PayFast URL
        $payfast_url = $this->sandbox ? 'https://sandbox.payfast.co.za/eng/process' : 'https://www.payfast.co.za/eng/process';
        
        return [
            'url' => $payfast_url,
            'data' => $data
        ];
    }
    
    /**
     * Generate payment signature
     */
    private function generateSignature($data) {
        // Create parameter string
        $param_string = '';
        foreach ($data as $key => $value) {
            if ($key !== 'signature') {
                $param_string .= $key . '=' . urlencode(trim($value)) . '&';
            }
        }
        
        // Remove last ampersand
        $param_string = rtrim($param_string, '&');
        
        // Add passphrase if set
        if (!empty($this->passphrase)) {
            $param_string .= '&passphrase=' . urlencode(trim($this->passphrase));
        }
        
        return md5($param_string);
    }
    
    /**
     * Verify payment notification
     */
    public function verifyPaymentNotification($post_data) {
        // Verify signature
        $signature = $post_data['signature'];
        unset($post_data['signature']);
        
        $calculated_signature = $this->generateSignature($post_data);
        
        if ($signature !== $calculated_signature) {
            return false;
        }
        
        // Verify payment status
        if ($post_data['payment_status'] !== 'COMPLETE') {
            return false;
        }
        
        return true;
    }
    
    /**
     * Update payment status
     */
    public function updatePaymentStatus($payment_reference, $status, $transaction_id = null) {
        $query = "UPDATE payments SET payment_status = :status, gateway_transaction_id = :transaction_id, updated_at = NOW() 
                  WHERE payment_reference = :payment_reference";
        
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':status', $status);
        $stmt->bindParam(':transaction_id', $transaction_id);
        $stmt->bindParam(':payment_reference', $payment_reference);
        
        return $stmt->execute();
    }
    
    /**
     * Get payment by reference
     */
    public function getPaymentByReference($payment_reference) {
        $query = "SELECT p.*, u.name as user_name, u.email as user_email, c.name as client_name, c.ftp_folder_path 
                  FROM payments p 
                  JOIN users u ON p.user_id = u.id 
                  JOIN clients c ON p.client_id = c.id 
                  WHERE p.payment_reference = :payment_reference";
        
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':payment_reference', $payment_reference);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            return $stmt->fetch(PDO::FETCH_ASSOC);
        }
        
        return false;
    }
    
    /**
     * Generate payment reference
     */
    private function generatePaymentReference() {
        return 'PAY_' . date('Ymd') . '_' . strtoupper(substr(uniqid(), -8));
    }
    
    /**
     * Process successful payment
     */
    public function processSuccessfulPayment($payment_reference, $transaction_id) {
        // Update payment status
        $this->updatePaymentStatus($payment_reference, 'completed', $transaction_id);
        
        // Get payment details
        $payment = $this->getPaymentByReference($payment_reference);
        
        if ($payment) {
            // Create success file in FTP
            try {
                require_once __DIR__ . '/FTPManager.php';
                $ftp = new FTPManager();
                $ftp->connect();
                
                $payment_data = [
                    'invoice_name' => $payment['invoice_name'],
                    'payment_reference' => $payment_reference,
                    'amount' => $payment['amount'],
                    'transaction_id' => $transaction_id
                ];
                
                $ftp->createPaymentSuccessFile(
                    $payment['ftp_folder_path'],
                    $payment['invoice_name'],
                    $payment_data
                );
                
                $ftp->disconnect();
                
                // Log successful payment
                error_log("Payment successful: " . $payment_reference . " for invoice: " . $payment['invoice_name']);
                
                return true;
            } catch (Exception $e) {
                error_log("Failed to create payment success file: " . $e->getMessage());
                return false;
            }
        }
        
        return false;
    }
    
    /**
     * Get client payments
     */
    public function getClientPayments($client_id, $limit = 50) {
        $query = "SELECT p.*, u.name as user_name 
                  FROM payments p 
                  JOIN users u ON p.user_id = u.id 
                  WHERE p.client_id = :client_id 
                  ORDER BY p.created_at DESC 
                  LIMIT :limit";
        
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':client_id', $client_id);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Calculate invoice amount (placeholder - implement your logic)
     */
    public function calculateInvoiceAmount($invoice_name) {
        // This is a placeholder. In a real application, you would:
        // 1. Parse the PDF to extract the amount
        // 2. Look up the amount in a database
        // 3. Use a predefined amount structure
        
        // For demo purposes, generate a random amount between R100 and R5000
        return rand(100, 5000);
    }
}
?>
