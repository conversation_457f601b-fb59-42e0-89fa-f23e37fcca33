# Portal Application

A secure multi-tenant web application built with PHP for managing invoices, payments, and business operations.

## Features

### 🔐 Security Features
- **Secure Authentication**: Password hashing with P<PERSON>'s `password_hash()`
- **Session Management**: Secure session handling with timeout protection
- **CSRF Protection**: Cross-Site Request Forgery protection on all forms
- **Input Validation**: Comprehensive input sanitization and validation
- **Multi-Tenancy**: Complete data isolation between clients
- **Role-Based Access**: Admin and user roles with module-specific permissions

### 👥 User Management
- **Admin Panel**: Create and manage users with role assignment
- **User Rights**: Granular module access control
- **Client Management**: Multi-tenant client organization
- **Session Monitoring**: Track user activity and login history

### 📄 Invoice Management
- **FTP Integration**: Read invoice PDFs from client-specific FTP folders
- **File Download**: Secure invoice file downloads
- **Payment Tracking**: Track payment status for each invoice
- **Multi-Tenant Isolation**: Each client sees only their invoices

### 💳 Payment Processing
- **PayFast Integration**: Secure payment gateway integration
- **Payment Tracking**: Complete payment lifecycle management
- **Success Notifications**: Automatic payment confirmation files
- **Multiple Payment States**: Pending, completed, failed, cancelled

### 📊 Dashboard & Modules
- **Role-Based Dashboard**: Show only accessible modules
- **Module System**: Invoices, Freight, Tariff, Accounting, Back Office
- **Quick Stats**: Payment summaries and statistics
- **Responsive Design**: Mobile-friendly interface

## Installation

### Prerequisites
- PHP 7.4 or higher
- MySQL 5.7 or higher
- Web server (Apache/Nginx)
- FTP server access
- PayFast merchant account (for payments)

### Setup Steps

1. **Clone/Download** the application files to your web server

2. **Database Setup**
   ```bash
   # Import the database schema
   mysql -u username -p < database/schema.sql
   ```

3. **Configuration**
   - Edit `config/database.php` with your database credentials
   - Edit `config/config.php` with your settings:
     - Base URL
     - FTP credentials
     - PayFast merchant details

4. **File Permissions**
   ```bash
   # Ensure proper permissions
   chmod 755 assets/
   chmod 644 assets/css/*
   chmod 644 assets/js/*
   ```

5. **FTP Setup**
   - Create client folders on your FTP server
   - Update client records with correct FTP paths

## Configuration

### Database Configuration
Edit `config/database.php`:
```php
private $host = 'localhost';
private $db_name = 'portal_app';
private $username = 'your_username';
private $password = 'your_password';
```

### Application Configuration
Edit `config/config.php`:
```php
define('BASE_URL', 'https://yourdomain.com/portal/');
define('FTP_HOST', 'your-ftp-server.com');
define('FTP_USERNAME', 'ftp_username');
define('FTP_PASSWORD', 'ftp_password');
```

### PayFast Configuration
```php
define('PAYFAST_MERCHANT_ID', 'your_merchant_id');
define('PAYFAST_MERCHANT_KEY', 'your_merchant_key');
define('PAYFAST_PASSPHRASE', 'your_passphrase');
define('PAYFAST_SANDBOX', false); // Set to true for testing
```

## Default Credentials

### Admin Account
- **Email**: <EMAIL>
- **Password**: admin123
- **Access**: All modules

### Demo User Account
- **Email**: <EMAIL>
- **Password**: admin123
- **Access**: Invoices and Accounting modules

**⚠️ Important**: Change these default passwords immediately after installation!

## Security Features

### Authentication
- Secure password hashing using `PASSWORD_DEFAULT`
- Session-based authentication with timeout
- Failed login attempt logging
- Secure session cookie settings

### Authorization
- Role-based access control (Admin/User)
- Module-specific permissions
- Multi-tenant data isolation
- CSRF token protection

### Input Validation
- All user inputs are sanitized
- SQL injection prevention with prepared statements
- XSS protection with `htmlspecialchars()`
- File upload validation and restrictions

### Session Security
- Secure session configuration
- Session timeout protection
- Session regeneration on login
- Automatic session cleanup

## File Structure

```
portal-dev/
├── admin/
│   └── users.php              # User management interface
├── api/
│   ├── check_session.php      # Session validation
│   ├── get_clients.php        # Client data API
│   ├── get_csrf_token.php     # CSRF token refresh
│   ├── get_user.php           # User data API
│   └── refresh_session.php    # Session extension
├── assets/
│   ├── css/
│   │   └── style.css          # Application styles
│   └── js/
│       ├── admin.js           # Admin panel functionality
│       └── script.js          # Main application scripts
├── classes/
│   ├── Client.php             # Client management
│   ├── FTPManager.php         # FTP operations
│   ├── PaymentGateway.php     # Payment processing
│   └── User.php               # User management
├── config/
│   ├── config.php             # Application configuration
│   └── database.php           # Database connection
├── database/
│   └── schema.sql             # Database schema
├── modules/
│   ├── accounting.php         # Accounting module
│   ├── backoffice.php         # Back office module
│   ├── freight.php            # Freight module
│   ├── invoices.php           # Invoice management
│   ├── payment.php            # Payment processing
│   ├── payment_cancel.php     # Payment cancellation
│   ├── payment_notify.php     # Payment notifications
│   ├── payment_return.php     # Payment return handler
│   └── tariff.php             # Tariff module
├── dashboard.php              # Main dashboard
├── login.php                  # Login page
├── logout.php                 # Logout handler
└── README.md                  # This file
```

## Usage

### For Administrators
1. Log in with admin credentials
2. Access the Admin Panel to create users
3. Assign module rights to users
4. Manage clients and their FTP folders

### For Users
1. Log in with user credentials
2. Access assigned modules from the dashboard
3. View and download invoices
4. Make payments through the secure gateway

## Payment Flow

1. User selects "Pay Now" on an invoice
2. Payment record is created in the database
3. User is redirected to PayFast for payment
4. PayFast processes the payment
5. Payment notification is received via ITN
6. Payment status is updated in the database
7. Success file is created in the client's FTP folder
8. User is redirected back with payment result

## Troubleshooting

### Common Issues

**Database Connection Failed**
- Check database credentials in `config/database.php`
- Ensure MySQL server is running
- Verify database exists and user has permissions

**FTP Connection Failed**
- Verify FTP credentials in `config/config.php`
- Check FTP server accessibility
- Ensure client folders exist on FTP server

**Payment Issues**
- Verify PayFast credentials
- Check PayFast sandbox/live mode setting
- Review PayFast ITN logs in server error log

**Session Timeout**
- Adjust `SESSION_TIMEOUT` in `config/config.php`
- Check server session configuration
- Verify session storage permissions

## Security Considerations

1. **Change Default Passwords**: Update all default credentials
2. **Use HTTPS**: Always use SSL/TLS in production
3. **Regular Updates**: Keep PHP and dependencies updated
4. **File Permissions**: Set appropriate file and directory permissions
5. **Error Logging**: Monitor error logs for security issues
6. **Backup Strategy**: Implement regular database and file backups

## Support

For technical support or questions:
1. Check the troubleshooting section
2. Review server error logs
3. Verify configuration settings
4. Contact your system administrator

## License

This application is proprietary software. All rights reserved.
