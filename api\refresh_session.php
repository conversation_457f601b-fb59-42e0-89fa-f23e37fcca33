<?php
/**
 * Refresh Session API
 * Extends current session
 */

require_once '../config/config.php';

header('Content-Type: application/json');

$response = [
    'success' => false,
    'message' => 'Session invalid'
];

if (isLoggedIn()) {
    $_SESSION['last_activity'] = time();
    
    $response = [
        'success' => true,
        'message' => 'Session refreshed',
        'expires_in' => SESSION_TIMEOUT
    ];
}

echo json_encode($response);
?>
