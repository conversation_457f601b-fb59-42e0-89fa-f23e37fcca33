<?php
/**
 * Get Clients API
 * Returns list of clients for dropdowns
 */

require_once '../config/config.php';
require_once '../classes/Client.php';

// Require admin access
requireAdmin();

header('Content-Type: application/json');

$database = new Database();
$db = $database->getConnection();
$client = new Client($db);

// Get all clients
$clients = $client->getAll();

$response = [
    'success' => true,
    'clients' => $clients
];

echo json_encode($response);
?>
