<?php
/**
 * Admin User Management
 * Create, edit, and manage users and their rights
 */

require_once '../config/config.php';
require_once '../classes/User.php';
require_once '../classes/Client.php';

// Require admin access
requireAdmin();

$database = new Database();
$db = $database->getConnection();
$user = new User($db);
$client = new Client($db);

$error_message = '';
$success_message = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        $error_message = 'Invalid request. Please try again.';
    } else {
        $action = $_POST['action'];
        
        switch ($action) {
            case 'create_user':
                $name = sanitizeInput($_POST['name']);
                $email = sanitizeInput($_POST['email']);
                $password = $_POST['password'];
                $role = sanitizeInput($_POST['role']);
                $client_id = (int)$_POST['client_id'];
                $modules = isset($_POST['modules']) ? $_POST['modules'] : [];
                
                // Validate input
                if (empty($name) || empty($email) || empty($password)) {
                    $error_message = 'Please fill in all required fields.';
                } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                    $error_message = 'Please enter a valid email address.';
                } elseif ($user->emailExists($email)) {
                    $error_message = 'Email address already exists.';
                } elseif (strlen($password) < 6) {
                    $error_message = 'Password must be at least 6 characters long.';
                } else {
                    // Create user
                    $user->name = $name;
                    $user->email = $email;
                    $user->password = $password;
                    $user->role = $role;
                    $user->client_id = $client_id;
                    $user->status = 'active';
                    
                    if ($user->create()) {
                        // Set user rights
                        $user->setUserRights($user->id, $modules);
                        $success_message = 'User created successfully.';
                    } else {
                        $error_message = 'Failed to create user. Please try again.';
                    }
                }
                break;
                
            case 'edit_user':
                $user_id = (int)$_POST['user_id'];
                $name = sanitizeInput($_POST['name']);
                $email = sanitizeInput($_POST['email']);
                $role = sanitizeInput($_POST['role']);
                $client_id = (int)$_POST['client_id'];
                $status = sanitizeInput($_POST['status']);
                $modules = isset($_POST['modules']) ? $_POST['modules'] : [];
                
                // Validate input
                if (empty($name) || empty($email)) {
                    $error_message = 'Please fill in all required fields.';
                } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                    $error_message = 'Please enter a valid email address.';
                } elseif ($user->emailExists($email, $user_id)) {
                    $error_message = 'Email address already exists.';
                } else {
                    // Update user
                    $user->id = $user_id;
                    $user->name = $name;
                    $user->email = $email;
                    $user->role = $role;
                    $user->client_id = $client_id;
                    $user->status = $status;
                    
                    if ($user->update()) {
                        // Update user rights
                        $user->setUserRights($user_id, $modules);
                        $success_message = 'User updated successfully.';
                    } else {
                        $error_message = 'Failed to update user. Please try again.';
                    }
                }
                break;
                
            case 'delete_user':
                $user_id = (int)$_POST['user_id'];
                
                // Don't allow deleting own account
                if ($user_id == $_SESSION['user_id']) {
                    $error_message = 'You cannot delete your own account.';
                } else {
                    if ($user->delete($user_id)) {
                        $success_message = 'User deleted successfully.';
                    } else {
                        $error_message = 'Failed to delete user. Please try again.';
                    }
                }
                break;
        }
    }
}

// Get all users and clients
$users = $user->getAll();
$clients = $client->getAll();

// Generate CSRF token
$csrf_token = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Management - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body class="dashboard">
    <header class="header">
        <div class="header-content">
            <div class="header-left">
                <div class="brand-section">
                    <div class="brand-logo">
                        <img src="../assets/img/logo.png" alt="<?php echo APP_NAME; ?>" class="logo-image">
                    </div>
                    <div class="brand-text">
                        <span class="brand-subtitle">Admin Panel</span>
                    </div>
                </div>
            </div>
            <div class="header-right">
                <div class="user-info">
                    <a href="../dashboard.php" class="btn btn-sm btn-secondary">Back to Dashboard</a>
                    <a href="../logout.php" class="btn btn-sm btn-danger">Logout</a>
                </div>
            </div>
        </div>
    </header>
    
    <main class="main-content">
        <h2>User Management</h2>
        
        <?php if ($error_message): ?>
            <div class="alert alert-error">
                <?php echo htmlspecialchars($error_message); ?>
            </div>
        <?php endif; ?>
        
        <?php if ($success_message): ?>
            <div class="alert alert-success">
                <?php echo htmlspecialchars($success_message); ?>
            </div>
        <?php endif; ?>
        
        <!-- Create User Form -->
        <div class="form-section">
            <h3>Create New User</h3>
            <form method="POST" class="form">
                <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                <input type="hidden" name="action" value="create_user">
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="name">Full Name</label>
                        <input type="text" id="name" name="name" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="email">Email Address</label>
                        <input type="email" id="email" name="email" required>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="password">Password</label>
                        <input type="password" id="password" name="password" required minlength="6">
                    </div>
                    
                    <div class="form-group">
                        <label for="role">Role</label>
                        <select id="role" name="role" required>
                            <option value="user">User</option>
                            <option value="admin">Admin</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="client_id">Client</label>
                        <select id="client_id" name="client_id" required>
                            <option value="">Select Client</option>
                            <?php foreach ($clients as $client_item): ?>
                                <option value="<?php echo $client_item['id']; ?>">
                                    <?php echo htmlspecialchars($client_item['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <label>Module Access Rights</label>
                    <div class="checkbox-group">
                        <?php foreach (AVAILABLE_MODULES as $module_key => $module_name): ?>
                            <label class="checkbox-label">
                                <input type="checkbox" name="modules[]" value="<?php echo $module_key; ?>">
                                <?php echo $module_name; ?>
                            </label>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <button type="submit" class="btn btn-primary">Create User</button>
            </form>
        </div>
        
        <!-- Users List -->
        <div class="table-container">
            <h3>Existing Users</h3>
            <table class="table">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Email</th>
                        <th>Role</th>
                        <th>Client</th>
                        <th>Status</th>
                        <th>Last Login</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($users as $user_item): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($user_item['name']); ?></td>
                            <td><?php echo htmlspecialchars($user_item['email']); ?></td>
                            <td>
                                <span class="role-badge role-<?php echo $user_item['role']; ?>">
                                    <?php echo ucfirst($user_item['role']); ?>
                                </span>
                            </td>
                            <td><?php echo htmlspecialchars($user_item['client_name']); ?></td>
                            <td>
                                <span class="status-badge status-<?php echo $user_item['status']; ?>">
                                    <?php echo ucfirst($user_item['status']); ?>
                                </span>
                            </td>
                            <td>
                                <?php echo $user_item['last_login'] ? date('Y-m-d H:i', strtotime($user_item['last_login'])) : 'Never'; ?>
                            </td>
                            <td>
                                <button onclick="editUser(<?php echo $user_item['id']; ?>)" class="btn btn-sm btn-secondary">Edit</button>
                                <?php if ($user_item['id'] != $_SESSION['user_id']): ?>
                                    <button onclick="deleteUser(<?php echo $user_item['id']; ?>)" class="btn btn-sm btn-danger">Delete</button>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </main>
    
    <script src="../assets/js/script.js"></script>
    <script src="../assets/js/admin.js"></script>
</body>
</html>
