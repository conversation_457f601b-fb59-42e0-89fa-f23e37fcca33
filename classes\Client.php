<?php
/**
 * Client Class
 * Handles client/tenant management
 */

require_once __DIR__ . '/../config/config.php';

class Client {
    private $conn;
    private $table_name = "clients";
    
    public $id;
    public $name;
    public $ftp_folder_path;
    public $status;
    
    public function __construct($db) {
        $this->conn = $db;
    }
    
    /**
     * Create new client
     */
    public function create() {
        $query = "INSERT INTO " . $this->table_name . " 
                  SET name = :name, ftp_folder_path = :ftp_folder_path, status = :status";
        
        $stmt = $this->conn->prepare($query);
        
        $stmt->bindParam(':name', $this->name);
        $stmt->bindParam(':ftp_folder_path', $this->ftp_folder_path);
        $stmt->bindParam(':status', $this->status);
        
        if ($stmt->execute()) {
            $this->id = $this->conn->lastInsertId();
            return true;
        }
        
        return false;
    }
    
    /**
     * Get client by ID
     */
    public function getById($id) {
        $query = "SELECT * FROM " . $this->table_name . " WHERE id = :id";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            return $stmt->fetch(PDO::FETCH_ASSOC);
        }
        
        return false;
    }
    
    /**
     * Get all clients
     */
    public function getAll() {
        $query = "SELECT * FROM " . $this->table_name . " ORDER BY name ASC";
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Update client
     */
    public function update() {
        $query = "UPDATE " . $this->table_name . " 
                  SET name = :name, ftp_folder_path = :ftp_folder_path, status = :status 
                  WHERE id = :id";
        
        $stmt = $this->conn->prepare($query);
        
        $stmt->bindParam(':name', $this->name);
        $stmt->bindParam(':ftp_folder_path', $this->ftp_folder_path);
        $stmt->bindParam(':status', $this->status);
        $stmt->bindParam(':id', $this->id);
        
        return $stmt->execute();
    }
    
    /**
     * Delete client
     */
    public function delete($id) {
        $query = "DELETE FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        
        return $stmt->execute();
    }
    
    /**
     * Check if client name exists
     */
    public function nameExists($name, $exclude_id = null) {
        $query = "SELECT id FROM " . $this->table_name . " WHERE name = :name";
        
        if ($exclude_id) {
            $query .= " AND id != :exclude_id";
        }
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':name', $name);
        
        if ($exclude_id) {
            $stmt->bindParam(':exclude_id', $exclude_id);
        }
        
        $stmt->execute();
        
        return $stmt->rowCount() > 0;
    }
    
    /**
     * Get client's FTP folder path
     */
    public function getFtpPath($client_id) {
        $query = "SELECT ftp_folder_path FROM " . $this->table_name . " WHERE id = :id AND status = 'active'";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $client_id);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            return $row['ftp_folder_path'];
        }
        
        return false;
    }
}
?>
