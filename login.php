<?php
/**
 * Login Page
 * Secure login form with CSRF protection
 */

require_once 'config/config.php';
require_once 'classes/User.php';

// Redirect if already logged in
if (isLoggedIn()) {
    header('Location: dashboard.php');
    exit();
}

$error_message = '';
$success_message = '';

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verify CSRF token
    if (!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
        $error_message = 'Invalid request. Please try again.';
    } else {
        $email = sanitizeInput($_POST['email']);
        $password = $_POST['password'];
        
        // Validate input
        if (empty($email) || empty($password)) {
            $error_message = 'Please enter both email and password.';
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $error_message = 'Please enter a valid email address.';
        } else {
            // Attempt login
            $database = new Database();
            $db = $database->getConnection();
            $user = new User($db);
            
            if ($user->login($email, $password)) {
                header('Location: dashboard.php');
                exit();
            } else {
                $error_message = 'Invalid email or password.';
                // Log failed login attempt
                error_log("Failed login attempt for email: " . $email . " from IP: " . $_SERVER['REMOTE_ADDR']);
            }
        }
    }
}

// Generate CSRF token
$csrf_token = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Advanced Customs Solutions</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
            background: linear-gradient(135deg, rgba(43, 94, 95, 0.95), rgba(30, 74, 75, 0.95)),
                        url('https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80') center/cover no-repeat;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
            overflow: hidden;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80') center/cover no-repeat;
            opacity: 0.15;
            z-index: -2;
        }

        body::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(43, 94, 95, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(30, 74, 75, 0.3) 0%, transparent 50%),
                linear-gradient(135deg, rgba(43, 94, 95, 0.1) 0%, rgba(30, 74, 75, 0.1) 100%);
            z-index: -1;
        }

        .login-container {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(15px);
            border-radius: 16px;
            box-shadow: 0 25px 80px rgba(0,0,0,0.25), 0 8px 24px rgba(43, 94, 95, 0.15);
            overflow: hidden;
            width: 100%;
            max-width: 520px;
            border: 1px solid rgba(255,255,255,0.3);
            position: relative;
            z-index: 1;
            transform: translateZ(0);
        }

        .login-container::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(135deg, rgba(43, 94, 95, 0.4), transparent, rgba(43, 94, 95, 0.4));
            z-index: -1;
            border-radius: 18px;
            opacity: 0.5;
        }

        .login-header {
            background: linear-gradient(135deg, rgba(248, 250, 252, 0.95) 0%, rgba(241, 245, 249, 0.95) 100%);
            padding: 50px 40px 30px;
            text-align: center;
            border-bottom: 1px solid rgba(226, 232, 240, 0.8);
            position: relative;
            backdrop-filter: blur(5px);
        }

        .login-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #2B5E5F, #1e4a4b, #2B5E5F);
        }

        .login-header::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(43, 94, 95, 0.2), transparent);
        }

        .logo-container {
            margin-bottom: 25px;
            padding: 18px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 24px rgba(0,0,0,0.08), 0 2px 8px rgba(43, 94, 95, 0.1);
            display: inline-block;
            position: relative;
            overflow: hidden;
        }

        .logo-container::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, transparent, rgba(43, 94, 95, 0.05));
            z-index: 1;
        }

        .logo {
            width: 160px;
            height: auto;
            max-height: 90px;
            margin: 0 auto;
            display: block;
            filter: drop-shadow(0 2px 8px rgba(0,0,0,0.1));
            position: relative;
            z-index: 2;
        }

        .company-name {
            font-size: 26px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 12px;
            letter-spacing: -0.5px;
            text-shadow: 0 1px 2px rgba(0,0,0,0.05);
        }

        .login-subtitle {
            font-size: 16px;
            color: #64748b;
            font-weight: 500;
            margin-bottom: 8px;
        }

        .industry-tagline {
            font-size: 13px;
            color: #94a3b8;
            font-weight: 400;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 10px;
        }

        .login-form {
            padding: 40px 40px 45px;
            background: white;
            position: relative;
        }

        .login-form::before {
            content: '';
            position: absolute;
            top: 0;
            left: 20px;
            right: 20px;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(43, 94, 95, 0.1), transparent);
        }

        .alert {
            padding: 14px 18px;
            border-radius: 8px;
            margin-bottom: 25px;
            font-size: 14px;
            border: 1px solid;
            font-weight: 500;
        }

        .alert-error {
            background: #fef2f2;
            color: #dc2626;
            border-color: #fecaca;
            box-shadow: 0 2px 8px rgba(220, 38, 38, 0.1);
        }

        .alert-success {
            background: #f0fdf4;
            color: #16a34a;
            border-color: #bbf7d0;
            box-shadow: 0 2px 8px rgba(22, 163, 74, 0.1);
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #374151;
            font-size: 15px;
        }

        .form-group input {
            width: 100%;
            padding: 16px 20px;
            border: 2px solid #e5e7eb;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #fafbfc;
            color: #374151;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            position: relative;
        }

        .form-group input:focus {
            outline: none;
            border-color: #2B5E5F;
            background: white;
            box-shadow: 0 0 0 4px rgba(43, 94, 95, 0.1), 0 4px 12px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }

        .form-group input:hover {
            border-color: #d1d5db;
            box-shadow: 0 4px 8px rgba(0,0,0,0.08);
        }

        .form-group input::placeholder {
            color: #9ca3af;
            font-style: italic;
        }

        .btn {
            width: 100%;
            padding: 18px 24px;
            background: linear-gradient(135deg, #2B5E5F 0%, #1e4a4b 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 20px;
            box-shadow: 0 4px 12px rgba(43, 94, 95, 0.3);
            text-transform: uppercase;
            letter-spacing: 0.8px;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:hover {
            background: linear-gradient(135deg, #1e4a4b 0%, #163a3b 100%);
            transform: translateY(-3px);
            box-shadow: 0 8px 24px rgba(43, 94, 95, 0.4);
        }

        .btn:active {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(43, 94, 95, 0.3);
        }

        .login-footer {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            padding: 30px 40px;
            border-top: 1px solid #e2e8f0;
            font-size: 13px;
            color: #64748b;
            text-align: center;
            position: relative;
        }

        .login-footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 20px;
            right: 20px;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(43, 94, 95, 0.1), transparent);
        }

        .demo-credentials {
            margin-top: 15px;
            padding: 18px;
            background: white;
            border-radius: 10px;
            border: 1px solid #e2e8f0;
            font-size: 13px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
            position: relative;
            overflow: hidden;
        }

        .demo-credentials::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #2B5E5F, #1e4a4b, #2B5E5F);
        }

        .demo-credentials p {
            margin: 8px 0;
            color: #475569;
        }

        .demo-credentials strong {
            color: #1e293b;
            font-weight: 600;
        }

        .demo-credentials .credentials-title {
            color: #2B5E5F;
            font-weight: 700;
            margin-bottom: 12px;
            font-size: 14px;
        }

        /* Add subtle animation */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .login-container {
            animation: fadeInUp 0.6s ease-out;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <div class="logo-container">
                <img src="assets/img/logo.png" alt="ACS Logo" class="logo">
            </div>
            <div class="company-name">Advanced Customs Solutions</div>
            <div class="login-subtitle">Professional Import/Export Clearing Portal</div>
            <div class="industry-tagline">Streamlining Global Trade Operations</div>
        </div>

        <div class="login-form">
            <?php if ($error_message): ?>
                <div class="alert alert-error">
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php endif; ?>

            <?php if ($success_message): ?>
                <div class="alert alert-success">
                    <?php echo htmlspecialchars($success_message); ?>
                </div>
            <?php endif; ?>

            <form method="POST" action="login.php">
                <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">

                <div class="form-group">
                    <label for="email">User name or email:</label>
                    <input type="email" id="email" name="email" required
                           value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>"
                           placeholder="">
                </div>

                <div class="form-group">
                    <label for="password">Password:</label>
                    <input type="password" id="password" name="password" required
                           placeholder="password">
                </div>

                <button type="submit" class="btn">Log in</button>
            </form>
        </div>

        <div class="login-footer">
            <div class="demo-credentials">
                <div class="credentials-title">Demo Access Credentials</div>
                <p><strong>Administrator:</strong> <EMAIL> / admin123</p>
                <p><strong>Client User:</strong> <EMAIL> / admin123</p>
                <p style="margin-top: 12px; font-size: 12px; color: #94a3b8;">
                    <em>For demonstration purposes only</em>
                </p>
            </div>
        </div>
    </div>
</body>
</html>
